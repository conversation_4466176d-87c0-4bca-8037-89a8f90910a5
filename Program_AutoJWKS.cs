using AuthService.Configuration;
using AuthService.Services;
using Microsoft.AspNetCore.DataProtection;
using DotNetEnv;

// Create a logger for startup configuration
using var loggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddConsole();
    builder.SetMinimumLevel(LogLevel.Debug);
});
var logger = loggerFactory.CreateLogger<Program>();

// Load environment variables from .env file if it exists
if (File.Exists(Path.Combine(Directory.GetCurrentDirectory(), ".env")))
{
    Env.Load();
    logger.LogDebug("Loaded environment variables from .env file");
}

var builder = WebApplication.CreateBuilder(args);

// Validate required Supabase configuration
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseAnonKey = builder.Configuration["SUPABASE_ANON_KEY"];
var supabaseProjectId = builder.Configuration["SUPABASE_PROJECT_ID"];

if (!builder.Environment.IsEnvironment("Testing"))
{
    if (string.IsNullOrEmpty(supabaseUrl))
        throw new InvalidOperationException("SUPABASE_URL must be configured");
    
    if (string.IsNullOrEmpty(supabaseAnonKey))
        throw new InvalidOperationException("SUPABASE_ANON_KEY must be configured");
    
    if (string.IsNullOrEmpty(supabaseProjectId))
        throw new InvalidOperationException("SUPABASE_PROJECT_ID must be configured");
}

// 🚀 AUTO-CONFIGURE SUPABASE JWT AUTHENTICATION
if (!builder.Environment.IsEnvironment("Testing"))
{
    builder.Services.AddSupabaseJwtAuthentication(builder.Configuration, logger);
}

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddControllers();
builder.Services.AddAuthorization();

// Configure data protection
builder.Services.AddDataProtection()
    .SetApplicationName("auth-service");

builder.Services.AddSwaggerGen();

// Register services
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();
builder.Services.AddScoped<ICacheService, MemoryCacheService>();
builder.Services.AddScoped<ISupabaseClient, SupabaseClient>();

builder.Services.AddHealthChecks();

var app = builder.Build();

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Auth Service API v1");
        c.RoutePrefix = string.Empty;
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

if (app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in Auth service");
    return Results.Problem("An error occurred while processing your request.");
});

// Root endpoint
app.MapGet("/", () => new
{
    service = "Auth Service",
    version = "1.0.0",
    authentication = "Auto-configured Supabase JWT with JWKS discovery"
});

// Test endpoint for JWT validation
app.MapGet("/auth/test-jwt", (HttpContext context) =>
{
    var user = context.User;

    if (!user.Identity?.IsAuthenticated == true)
    {
        return Results.Unauthorized();
    }

    var claims = user.Claims.ToDictionary(c => c.Type, c => c.Value);

    return Results.Ok(new
    {
        Message = "🎉 JWT validation successful with auto-configured JWKS!",
        UserId = user.FindFirst("sub")?.Value,
        Email = user.FindFirst("email")?.Value,
        Role = user.FindFirst("role")?.Value,
        Audience = user.FindFirst("aud")?.Value,
        Issuer = user.FindFirst("iss")?.Value,
        Algorithm = context.Items["jwt_algorithm"]?.ToString(),
        KeyId = context.Items["jwt_key_id"]?.ToString(),
        ExpiresAt = user.FindFirst("exp")?.Value,
        IssuedAt = user.FindFirst("iat")?.Value,
        AllClaims = claims
    });
}).RequireAuthorization();

// JWKS configuration status endpoint
app.MapGet("/auth/jwks-status", async (IServiceProvider serviceProvider) =>
{
    try
    {
        var configurationManager = serviceProvider.GetService<Microsoft.IdentityModel.Protocols.IConfigurationManager<Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectConfiguration>>();
        
        if (configurationManager != null)
        {
            var config = await configurationManager.GetConfigurationAsync(CancellationToken.None);
            
            return Results.Ok(new
            {
                Status = "Configured",
                Issuer = config.Issuer,
                JwksUri = config.JwksUri,
                SigningKeysCount = config.SigningKeys.Count,
                SigningKeys = config.SigningKeys.Select(k => new
                {
                    KeyId = k.Kid,
                    Algorithm = k.Alg,
                    KeyType = k.Kty,
                    Use = k.Use
                }).ToList()
            });
        }
        
        return Results.Ok(new { Status = "Not configured" });
    }
    catch (Exception ex)
    {
        return Results.Ok(new 
        { 
            Status = "Error", 
            Error = ex.Message 
        });
    }
});

app.Run();

public partial class Program { }
