using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Net.Http.Headers;

namespace AuthService.Configuration;

/// <summary>
/// Simplified Supabase JWT configuration with automatic JWKS discovery
/// </summary>
public static class SimpleSupabaseJwtConfiguration
{
    /// <summary>
    /// Adds Supabase JWT authentication with automatic JWKS discovery
    /// This is the simplest way to configure Supabase JWT validation
    /// </summary>
    public static IServiceCollection AddSimpleSupabaseJwt(
        this IServiceCollection services,
        IConfiguration configuration,
        ILogger? logger = null)
    {
        var supabaseUrl = configuration["SUPABASE_URL"];
        var supabaseAnonKey = configuration["SUPABASE_ANON_KEY"];
        var supabaseProjectId = configuration["SUPABASE_PROJECT_ID"];

        // Validate configuration
        if (string.IsNullOrEmpty(supabaseUrl))
            throw new InvalidOperationException("SUPABASE_URL is required");
        if (string.IsNullOrEmpty(supabaseAnonKey))
            throw new InvalidOperationException("SUPABASE_ANON_KEY is required");
        if (string.IsNullOrEmpty(supabaseProjectId))
            throw new InvalidOperationException("SUPABASE_PROJECT_ID is required");

        logger?.LogInformation("🔧 Configuring Supabase JWT for: {SupabaseUrl}", supabaseUrl);

        // Register a custom HttpClient for JWKS requests
        services.AddHttpClient("SupabaseJwks", client =>
        {
            client.DefaultRequestHeaders.Add("apikey", supabaseAnonKey);
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        // Configure JWT Bearer authentication
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                var issuer = $"{supabaseUrl}/auth/v1";
                var jwksUri = $"{supabaseUrl}/auth/v1/jwks";

                logger?.LogInformation("🔑 JWT Issuer: {Issuer}", issuer);
                logger?.LogInformation("🔑 JWKS URI: {JwksUri}", jwksUri);

                // Basic token validation parameters
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = issuer,
                    ValidateAudience = true,
                    ValidAudiences = new[] { "authenticated", supabaseProjectId },
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromSeconds(30),
                    ValidateIssuerSigningKey = true,
                    RequireSignedTokens = true,
                    RequireExpirationTime = true
                };

                // Configure JWKS retrieval with custom HttpClient
                options.MetadataAddress = jwksUri;
                options.RequireHttpsMetadata = !Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.Equals("Development", StringComparison.OrdinalIgnoreCase) == true;

                // Use custom HttpClient for JWKS requests
                options.BackchannelHttpHandler = new SupabaseJwksHandler(
                    services.BuildServiceProvider().GetRequiredService<IHttpClientFactory>(),
                    supabaseAnonKey,
                    logger
                );

                // Refresh JWKS every hour, cache for 24 hours
                options.RefreshInterval = TimeSpan.FromHours(1);
                options.AutomaticRefreshInterval = TimeSpan.FromHours(24);

                // Event handlers for debugging
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        logger?.LogError("❌ JWT Authentication failed: {Error}", context.Exception.Message);
                        
                        // Log specific error types
                        switch (context.Exception)
                        {
                            case SecurityTokenInvalidSignatureException:
                                logger?.LogError("🔐 Signature validation failed - check JWKS configuration");
                                break;
                            case SecurityTokenInvalidIssuerException:
                                logger?.LogError("🏢 Invalid issuer - expected: {ExpectedIssuer}", issuer);
                                break;
                            case SecurityTokenInvalidAudienceException:
                                logger?.LogError("👥 Invalid audience - expected: authenticated or {ProjectId}", supabaseProjectId);
                                break;
                            case SecurityTokenExpiredException:
                                logger?.LogWarning("⏰ Token expired");
                                break;
                        }
                        
                        return Task.CompletedTask;
                    },
                    
                    OnTokenValidated = context =>
                    {
                        var userId = context.Principal?.FindFirst("sub")?.Value;
                        var email = context.Principal?.FindFirst("email")?.Value;
                        
                        logger?.LogInformation("✅ JWT validated successfully - User: {UserId}, Email: {Email}", 
                            userId, email);
                        
                        return Task.CompletedTask;
                    },
                    
                    OnMessageReceived = context =>
                    {
                        // Extract and log token info for debugging
                        if (!string.IsNullOrEmpty(context.Token))
                        {
                            try
                            {
                                var parts = context.Token.Split('.');
                                if (parts.Length >= 2)
                                {
                                    var header = DecodeJwtPart(parts[0]);
                                    var headerData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, System.Text.Json.JsonElement>>(header);
                                    
                                    if (headerData != null)
                                    {
                                        var alg = headerData.TryGetValue("alg", out var algValue) ? algValue.GetString() : "unknown";
                                        var kid = headerData.TryGetValue("kid", out var kidValue) ? kidValue.GetString() : "unknown";
                                        
                                        logger?.LogDebug("🔍 JWT received - Algorithm: {Algorithm}, KeyId: {KeyId}", alg, kid);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                logger?.LogWarning(ex, "Error parsing JWT header");
                            }
                        }
                        
                        return Task.CompletedTask;
                    }
                };
            });

        logger?.LogInformation("✅ Supabase JWT authentication configured successfully");
        return services;
    }

    private static string DecodeJwtPart(string base64Url)
    {
        var base64 = base64Url.PadRight(base64Url.Length + (4 - base64Url.Length % 4) % 4, '=')
            .Replace('-', '+')
            .Replace('_', '/');
        
        return System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(base64));
    }
}

/// <summary>
/// Custom HTTP handler for Supabase JWKS requests that includes the API key
/// </summary>
public class SupabaseJwksHandler : HttpClientHandler
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly string _supabaseAnonKey;
    private readonly ILogger? _logger;

    public SupabaseJwksHandler(IHttpClientFactory httpClientFactory, string supabaseAnonKey, ILogger? logger)
    {
        _httpClientFactory = httpClientFactory;
        _supabaseAnonKey = supabaseAnonKey;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Add Supabase API key to JWKS requests
        if (request.RequestUri?.AbsolutePath.Contains("/jwks") == true)
        {
            request.Headers.Add("apikey", _supabaseAnonKey);
            _logger?.LogDebug("🔑 Added API key to JWKS request: {Uri}", request.RequestUri);
        }

        var response = await base.SendAsync(request, cancellationToken);
        
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger?.LogError("❌ JWKS request failed: {StatusCode} - {Content}", response.StatusCode, content);
        }
        else
        {
            _logger?.LogDebug("✅ JWKS request successful: {StatusCode}", response.StatusCode);
        }

        return response;
    }
}
