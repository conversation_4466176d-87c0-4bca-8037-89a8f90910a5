import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/auth_client.dart';
import '../l10n/app_localizations.dart';
import '../widgets/jwt_analyzer_widget.dart';

/// Full-screen JWT token analyzer with additional testing capabilities
class JwtAnalyzerScreen extends StatefulWidget {
  const JwtAnalyzerScreen({super.key});

  @override
  State<JwtAnalyzerScreen> createState() => _JwtAnalyzerScreenState();
}

class _JwtAnalyzerScreenState extends State<JwtAnalyzerScreen> {
  bool _isTestingValidation = false;
  Map<String, dynamic>? _validationResult;

  @override
  Widget build(BuildContext context) {
    final locale = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(locale?.analyzeJwtToken ?? 'JWT Token Analyzer'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showHelpDialog,
            icon: const Icon(Icons.help_outline),
            tooltip: 'Help',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Main JWT Analyzer Widget
            const JwtAnalyzerWidget(),

            // Server Validation Test Section
            _buildServerValidationSection(),

            // Quick Actions Section
            _buildQuickActionsSection(),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildServerValidationSection() {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud_sync, color: theme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Server Validation Test',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Test if your server can validate the current JWT token',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isTestingValidation ? null : _testServerValidation,
                icon:
                    _isTestingValidation
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.verified_user),
                label: Text(
                  _isTestingValidation
                      ? 'Testing Server...'
                      : 'Test Server Validation',
                ),
              ),
            ),

            if (_validationResult != null) ...[
              const SizedBox(height: 16),
              _buildValidationResult(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildValidationResult() {
    final result = _validationResult!;
    final isSuccess = !result.containsKey('error');
    final color = isSuccess ? Colors.green : Colors.red;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                isSuccess
                    ? 'Server Validation Successful!'
                    : 'Server Validation Failed',
                style: TextStyle(color: color, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (isSuccess) ...[
            Text('✅ User ID: ${result['user_id'] ?? 'Unknown'}'),
            Text('✅ Email: ${result['email'] ?? 'Unknown'}'),
            Text('✅ Algorithm: ${result['algorithm'] ?? 'Unknown'}'),
          ] else ...[
            Text('❌ Error: ${result['error']}', style: TextStyle(color: color)),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: theme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Quick Actions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _copyTokenForCurl,
                    icon: const Icon(Icons.terminal),
                    label: const Text('Copy for cURL'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _refreshToken,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh Token'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testServerValidation() async {
    setState(() {
      _isTestingValidation = true;
      _validationResult = null;
    });

    try {
      final result = await AuthClient.staticTestJwtValidation();
      setState(() {
        _validationResult = result ?? {'error': 'No response from server'};
        _isTestingValidation = false;
      });
    } catch (e) {
      setState(() {
        _validationResult = {'error': 'Test failed: $e'};
        _isTestingValidation = false;
      });
    }
  }

  Future<void> _copyTokenForCurl() async {
    try {
      final authClient = AuthClient();
      await authClient.initialize();
      final token = authClient.accessToken;

      if (token != null) {
        final curlCommand =
            '''curl -X GET "https://abraapp.undeclab.com/auth/test-jwt" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -H "Authorization: Bearer $token" \\
  -v''';

        await Clipboard.setData(ClipboardData(text: curlCommand));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('cURL command copied to clipboard! 🚀'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy cURL command: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshToken() async {
    try {
      final authClient = AuthClient();
      await authClient.initialize();

      // Trigger a token refresh
      await authClient.refreshAccessToken();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Token refreshed successfully! 🔄'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh token: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('JWT Token Analyzer Help'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'This tool helps you analyze and debug JWT tokens from Supabase.',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  Text('Features:'),
                  SizedBox(height: 8),
                  Text('• 🔍 Decode JWT header and payload'),
                  Text('• ⏰ Check token expiration status'),
                  Text('• 🔐 Verify token structure and claims'),
                  Text('• 🌐 Test server validation'),
                  Text('• 📋 Copy token for external testing'),
                  Text('• 🔄 Refresh expired tokens'),
                  SizedBox(height: 16),
                  Text(
                    'Use this when your server returns 401 errors to understand why JWT validation is failing.',
                    style: TextStyle(fontStyle: FontStyle.italic),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Got it!'),
              ),
            ],
          ),
    );
  }
}
