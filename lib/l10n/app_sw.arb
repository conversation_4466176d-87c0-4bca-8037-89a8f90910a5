{"@@locale": "sw", "pageTitle": "<PERSON><PERSON><PERSON> wa Nyumbani", "@pageTitle": {"description": "The title for the main application page"}, "welcomeMessage": "<PERSON><PERSON>, {userName}!", "@welcomeMessage": {"description": "A welcome message that includes the user's name.", "placeholders": {"userName": {"type": "String", "example": "Mwana"}}}, "login": "Ingia", "@login": {"description": "Text for the login button"}, "loginFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@loginFailed": {"description": "Message shown when login fails"}, "logout": "To<PERSON>", "@logout": {"description": "Text for the logout button"}, "logoutFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "@logoutFailed": {"description": "Message shown when logout fails"}, "profileTitle": "<PERSON><PERSON><PERSON>", "@profileTitle": {"description": "Title for the user profile screen"}, "settingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "@settingsTitle": {"description": "Title for the settings screen"}, "themeMode": "<PERSON><PERSON> ya <PERSON>", "@themeMode": {"description": "Title for the theme mode setting"}, "language": "<PERSON><PERSON><PERSON>", "@language": {"description": "Title for the language setting"}, "privacyPolicy": "<PERSON><PERSON> ya <PERSON>", "@privacyPolicy": {"description": "Title for the privacy policy setting"}, "checkForUpdates": "<PERSON><PERSON>", "@checkForUpdates": {"description": "Title for the check for updates setting"}, "notifications": "Arifa", "@notifications": {"description": "Title for the notifications setting"}, "termsOfService": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>", "@termsOfService": {"description": "Title for the terms of service setting"}, "about": "<PERSON><PERSON><PERSON>", "@about": {"description": "Title for the about setting"}, "errorNetwork": "Hitila<PERSON> ya mtandao. Tafadhali jaribu tena.", "@errorNetwork": {"description": "Generic network error message"}, "@@comment": "", "itemsCount": "{count, plural, =0{Hakuna kifungu} =1{Kifungu 1} other{{count} vifungu}}", "@itemsCount": {"description": "Message showing number of items", "placeholders": {"count": {"type": "num", "example": 3}}}, "watchlistTitle": "<PERSON><PERSON>", "@watchlistTitle": {"description": "Title for the watchlist screen"}, "portfolioTitle": "<PERSON><PERSON><PERSON>", "@portfolioTitle": {"description": "Title for the portfolio screen"}, "chartTitle": "<PERSON><PERSON>", "@chartTitle": {"description": "Title for the chart screen"}, "hubTitle": "<PERSON><PERSON>", "@hubTitle": {"description": "Title for the hub/market screen"}, "marketHubTitle": "<PERSON><PERSON> cha <PERSON>ko", "@marketHubTitle": {"description": "Title for the market hub screen"}, "rateUs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@rateUs": {"description": "Text for rate us button"}, "analyzeJwtToken": "Chunguza Tokeni ya JWT", "@analyzeJwtToken": {"description": "Text for JWT token analysis button"}, "askAlim": "<PERSON><PERSON><PERSON>", "@askAlim": {"description": "Text for AI assistant button"}, "tradingIdeas": "<PERSON><PERSON><PERSON>", "@tradingIdeas": {"description": "Title for trading ideas section"}, "marketAnalysis": "Uchambuzi wa Soko", "@marketAnalysis": {"description": "Title for market analysis section"}, "investmentTips": "Vidokezo vya Uwekezaji", "@investmentTips": {"description": "Title for investment tips section"}, "riskManagement": "Usima<PERSON><PERSON> wa <PERSON>", "@riskManagement": {"description": "Title for risk management section"}, "portfolioIdeas": "<PERSON><PERSON><PERSON>", "@portfolioIdeas": {"description": "Title for portfolio ideas section"}, "articleScreen": "<PERSON><PERSON><PERSON> ya Makala", "@articleScreen": {"description": "Tooltip for article screen button"}, "ideas": "<PERSON><PERSON><PERSON>", "@ideas": {"description": "Title for ideas section"}, "renameWatchlist": "<PERSON><PERSON><PERSON>", "@renameWatchlist": {"description": "Title for rename watchlist dialog"}, "watchlistName": "<PERSON><PERSON> la <PERSON> ya Ufuatiliaji", "@watchlistName": {"description": "Label for watchlist name input field"}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {"description": "Text for cancel button"}, "rename": "<PERSON><PERSON><PERSON>", "@rename": {"description": "Text for rename button"}, "save": "<PERSON><PERSON><PERSON>", "@save": {"description": "Text for save button"}, "delete": "<PERSON><PERSON>", "@delete": {"description": "Text for delete button"}, "edit": "<PERSON><PERSON>", "@edit": {"description": "Text for edit button"}, "add": "Ongeza", "@add": {"description": "Text for add button"}, "search": "<PERSON><PERSON><PERSON>", "@search": {"description": "Text for search functionality"}, "loading": "Inapakia...", "@loading": {"description": "Text shown when content is loading"}, "error": "<PERSON><PERSON><PERSON>", "@error": {"description": "Generic error text"}, "retry": "<PERSON><PERSON><PERSON>", "@retry": {"description": "Text for retry button"}, "noData": "Hakuna data inayopatikana", "@noData": {"description": "Message when no data is available"}, "connectionError": "<PERSON>ila<PERSON> ya muunganisho. Tafadhali angalia muunganisho wako wa intaneti", "@connectionError": {"description": "Message shown when there's a connection error"}, "sessionExpired": "Ki<PERSON><PERSON> kimemalizika. Tafadhali ingia tena.", "@sessionExpired": {"description": "Message shown when user session expires"}, "jwtAnalysisMessage": "Inachunguza tokeni ya JWT... Angalia console ya debug kwa maelezo", "@jwtAnalysisMessage": {"description": "Message shown when analyzing JWT token"}, "jwtValidationFailed": "Uthibitisho wa JWT Umeshindikana: {error}", "@jwtValidationFailed": {"description": "Message shown when JWT validation fails", "placeholders": {"error": {"type": "String", "example": "Tokeni imemalizika"}}}, "jwtValidationSuccess": "Uthibitisho wa JWT Umefanikiwa! ✅", "@jwtValidationSuccess": {"description": "Message shown when JWT validation succeeds"}, "jwtAnalysisFailed": "Uchambuzi wa JWT umeshindikana: {error}", "@jwtAnalysisFailed": {"description": "Message shown when JWT analysis fails", "placeholders": {"error": {"type": "String", "example": "Hit<PERSON>fu ya mtandao"}}}}