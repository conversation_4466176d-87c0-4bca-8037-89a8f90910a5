#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';

/// Standalone JWT token decoder script
/// Usage: dart scripts/jwt_decoder.dart [token]
/// If no token is provided, it will prompt for input
void main(List<String> args) {
  debugPrint('🔍 JWT Token Decoder');
  debugPrint('=' * 60);

  String? token;

  if (args.isNotEmpty) {
    token = args[0];
  } else {
    debugPrint('Enter JWT token (or press Enter to exit):');
    token = stdin.readLineSync();
  }

  if (token == null || token.trim().isEmpty) {
    debugPrint('❌ No token provided. Exiting.');
    exit(1);
  }

  token = token.trim();

  try {
    final result = decodeJwtToken(token);
    printTokenAnalysis(result);
  } catch (e) {
    debugPrint('❌ Error decoding token: $e');
    exit(1);
  }
}

/// Decode JWT token and return analysis results
Map<String, dynamic> decodeJwtToken(String token) {
  debugPrint('✅ Token found, length: ${token.length}');
  debugPrint(
    '🔑 Token (first 100 chars): ${token.substring(0, token.length > 100 ? 100 : token.length)}...',
  );

  // Split the JWT into its three parts
  final parts = token.split('.');
  if (parts.length != 3) {
    throw Exception(
      'Invalid JWT structure. Expected 3 parts, got ${parts.length}',
    );
  }

  debugPrint('✅ JWT has valid 3-part structure');

  final result = <String, dynamic>{
    'token_length': token.length,
    'parts_count': parts.length,
    'token_preview': token.substring(
      0,
      token.length > 100 ? 100 : token.length,
    ),
  };

  // Decode Header (Part 1)
  try {
    debugPrint('🔍 Decoding JWT Header...');
    String header = parts[0];

    // Add padding if needed for base64 decoding
    while (header.length % 4 != 0) {
      header += '=';
    }

    final headerDecoded = utf8.decode(base64Url.decode(header));
    final headerData = json.decode(headerDecoded) as Map<String, dynamic>;

    result['header'] = headerData;
    debugPrint('✅ JWT Header decoded successfully');
  } catch (e) {
    print('❌ Failed to decode JWT header: $e');
    result['header_error'] = e.toString();
  }

  // Decode Payload (Part 2)
  try {
    print('🔍 Decoding JWT Payload...');
    String payload = parts[1];

    // Add padding if needed for base64 decoding
    while (payload.length % 4 != 0) {
      payload += '=';
    }

    final payloadDecoded = utf8.decode(base64Url.decode(payload));
    final payloadData = json.decode(payloadDecoded) as Map<String, dynamic>;

    result['payload'] = payloadData;
    print('✅ JWT Payload decoded successfully');

    // Check token expiration
    final exp = payloadData['exp'] as int?;
    if (exp != null) {
      final expirationTime = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final now = DateTime.now();
      final isExpired = now.isAfter(expirationTime);

      result['expiration_info'] = {
        'expires_at': expirationTime.toIso8601String(),
        'expires_at_timestamp': exp,
        'current_time': now.toIso8601String(),
        'is_expired': isExpired,
        'time_until_expiry':
            isExpired ? 'EXPIRED' : expirationTime.difference(now).toString(),
      };
    }

    // Check issued at time
    final iat = payloadData['iat'] as int?;
    if (iat != null) {
      final issuedTime = DateTime.fromMillisecondsSinceEpoch(iat * 1000);
      result['issued_at'] = issuedTime.toIso8601String();
    }
  } catch (e) {
    print('❌ Failed to decode JWT payload: $e');
    result['payload_error'] = e.toString();
  }

  // Analyze Signature (Part 3)
  try {
    print('🔍 Analyzing JWT Signature...');
    final signature = parts[2];

    result['signature_info'] = {
      'length': signature.length,
      'preview': signature.substring(
        0,
        signature.length > 50 ? 50 : signature.length,
      ),
    };

    print('✅ JWT Signature analyzed');
  } catch (e) {
    print('❌ Failed to analyze JWT signature: $e');
    result['signature_error'] = e.toString();
  }

  return result;
}

/// Print comprehensive token analysis
void printTokenAnalysis(Map<String, dynamic> result) {
  print('');
  print('📊 TOKEN ANALYSIS RESULTS:');
  print('=' * 60);

  // Basic token info
  print('📏 Token Length: ${result['token_length']}');
  print('🔢 Parts Count: ${result['parts_count']}');
  print('👀 Token Preview: ${result['token_preview']}...');
  print('');

  // Header analysis
  if (result.containsKey('header')) {
    final header = result['header'] as Map<String, dynamic>;
    print('🔍 JWT HEADER:');
    print('   Algorithm: ${header['alg']}');
    print('   Type: ${header['typ']}');
    print('   Key ID: ${header['kid']}');
    print('');

    // Algorithm-specific advice
    final alg = header['alg'] as String?;
    if (alg != null) {
      print('🔐 ALGORITHM ANALYSIS:');
      if (alg.startsWith('ES')) {
        print('   ✅ Using ECDSA ($alg) - requires public key validation');
        print(
          '   🔑 Server needs to fetch public key from Supabase JWKS endpoint',
        );
        print(
          '   🌐 JWKS URL should be: https://your-project.supabase.co/auth/v1/jwks',
        );
      } else if (alg.startsWith('RS')) {
        print('   ✅ Using RSA ($alg) - requires public key validation');
        print(
          '   🔑 Server needs to fetch public key from Supabase JWKS endpoint',
        );
      } else if (alg.startsWith('HS')) {
        print('   ⚠️ Using HMAC ($alg) - requires shared secret');
        print('   🔑 Server needs the JWT secret from Supabase settings');
      }
      print('');
    }
  }

  // Payload analysis
  if (result.containsKey('payload')) {
    final payload = result['payload'] as Map<String, dynamic>;
    print('📦 JWT PAYLOAD:');
    print('   Issuer (iss): ${payload['iss']}');
    print('   Subject (sub): ${payload['sub']}');
    print('   Audience (aud): ${payload['aud']}');
    print('   Email: ${payload['email']}');
    print('   Role: ${payload['role']}');
    print('   JWT ID (jti): ${payload['jti']}');
    print('');

    // Check for Supabase-specific claims
    if (payload.containsKey('app_metadata')) {
      print('📱 App Metadata: ${payload['app_metadata']}');
    }
    if (payload.containsKey('user_metadata')) {
      print('👤 User Metadata: ${payload['user_metadata']}');
    }
    if (payload.containsKey('session_id')) {
      print('🔗 Session ID: ${payload['session_id']}');
    }
    print('');
  }

  // Expiration analysis
  if (result.containsKey('expiration_info')) {
    final expInfo = result['expiration_info'] as Map<String, dynamic>;
    print('⏰ EXPIRATION ANALYSIS:');
    print('   Expires At: ${expInfo['expires_at']}');
    print('   Current Time: ${expInfo['current_time']}');
    print('   Is Expired: ${expInfo['is_expired']}');
    print('   Time Until Expiry: ${expInfo['time_until_expiry']}');
    print('');

    if (expInfo['is_expired'] == true) {
      print('❌ TOKEN IS EXPIRED!');
      print('   This is likely why server validation is failing');
      print('   Solution: Refresh the token or re-authenticate');
      print('');
    }
  }

  // Signature analysis
  if (result.containsKey('signature_info')) {
    final sigInfo = result['signature_info'] as Map<String, dynamic>;
    print('✍️ SIGNATURE INFO:');
    print('   Length: ${sigInfo['length']}');
    print('   Preview: ${sigInfo['preview']}...');
    print('');
  }

  // Server validation recommendations
  print('🔧 SERVER VALIDATION RECOMMENDATIONS:');
  print('=' * 60);

  final header = result['header'] as Map<String, dynamic>?;
  final payload = result['payload'] as Map<String, dynamic>?;

  if (header != null && payload != null) {
    final alg = header['alg'] as String?;
    final iss = payload['iss'] as String?;
    final kid = header['kid'] as String?;

    if (alg != null && alg.startsWith('ES')) {
      print('1. ✅ Server should use ECDSA verification for $alg');
      print('2. 🔑 Server needs to fetch public key using kid: $kid');
      if (iss != null) {
        final jwksUrl = iss.endsWith('/') ? '${iss}jwks' : '$iss/jwks';
        print('3. 🌐 JWKS endpoint should be: $jwksUrl');
      }
      print('4. 📝 Server should validate:');
      print('   - Token signature using public key');
      print('   - Token expiration (exp claim)');
      print('   - Token issuer (iss claim)');
      print('   - Token audience (aud claim) if required');
    }

    if (payload['aud'] != null) {
      print('5. 🎯 Server should verify audience: ${payload['aud']}');
    }

    if (payload['role'] != null) {
      print('6. 👤 User role in token: ${payload['role']}');
    }
  }

  print('');
  print('🎉 JWT Token Analysis Complete!');
  print('=' * 60);
}
