import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/auth_client.dart';
import '../l10n/app_localizations.dart';

/// Widget that displays JWT token analysis in a user-friendly format
class JwtAnalyzerWidget extends StatefulWidget {
  const JwtAnalyzerWidget({super.key});

  @override
  State<JwtAnalyzerWidget> createState() => _JwtAnalyzerWidgetState();
}

class _JwtAnalyzerWidgetState extends State<JwtAnalyzerWidget> {
  bool _isAnalyzing = false;
  Map<String, dynamic>? _analysisResult;
  String? _error;

  @override
  Widget build(BuildContext context) {
    final locale = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.security,
                  color: theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  locale?.analyzeJwtToken ?? 'JWT Token Analyzer',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Analyze Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isAnalyzing ? null : _analyzeToken,
                icon: _isAnalyzing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.analytics),
                label: Text(
                  _isAnalyzing
                      ? (locale?.loading ?? 'Analyzing...')
                      : (locale?.analyzeJwtToken ?? 'Analyze JWT Token'),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Results
            if (_error != null) _buildErrorSection(),
            if (_analysisResult != null) _buildAnalysisResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error, color: Colors.red.shade700, size: 20),
              const SizedBox(width: 8),
              Text(
                'Error',
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: TextStyle(color: Colors.red.shade700),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisResults() {
    final result = _analysisResult!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Token Overview
        _buildSection(
          'Token Overview',
          Icons.info_outline,
          Colors.blue,
          [
            _buildInfoRow('Length', '${result['token_length']} characters'),
            _buildInfoRow('Parts', '${result['parts_count']}/3'),
            if (result['token_source'] != null)
              _buildInfoRow('Source', result['token_source']),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Token Header
        if (result['header'] != null) _buildHeaderSection(result['header']),
        
        const SizedBox(height: 16),
        
        // Token Claims
        if (result['payload'] != null) _buildPayloadSection(result['payload']),
        
        const SizedBox(height: 16),
        
        // Expiration Info
        if (result['expiration_info'] != null) 
          _buildExpirationSection(result['expiration_info']),
        
        const SizedBox(height: 16),
        
        // Validation Issues
        if (result['validation_issues'] != null)
          _buildValidationIssues(result['validation_issues']),
        
        const SizedBox(height: 16),
        
        // Full Token (Copyable)
        _buildFullTokenSection(),
      ],
    );
  }

  Widget _buildSection(String title, IconData icon, Color color, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...children,
        ],
      ),
    );
  }

  Widget _buildHeaderSection(Map<String, dynamic> header) {
    return _buildSection(
      'JWT Header',
      Icons.security,
      Colors.purple,
      [
        _buildInfoRow('Algorithm', header['alg'] ?? 'Unknown'),
        _buildInfoRow('Type', header['typ'] ?? 'Unknown'),
        _buildInfoRow('Key ID', header['kid'] ?? 'None'),
      ],
    );
  }

  Widget _buildPayloadSection(Map<String, dynamic> payload) {
    return _buildSection(
      'JWT Claims',
      Icons.account_circle,
      Colors.green,
      [
        _buildInfoRow('Issuer', payload['iss'] ?? 'Unknown'),
        _buildInfoRow('Subject', payload['sub'] ?? 'Unknown'),
        _buildInfoRow('Audience', payload['aud'] ?? 'Unknown'),
        _buildInfoRow('Email', payload['email'] ?? 'Unknown'),
        _buildInfoRow('Role', payload['role'] ?? 'Unknown'),
      ],
    );
  }

  Widget _buildExpirationSection(Map<String, dynamic> expInfo) {
    final isExpired = expInfo['is_expired'] == true;
    final color = isExpired ? Colors.red : Colors.green;
    
    return _buildSection(
      'Token Expiration',
      isExpired ? Icons.warning : Icons.check_circle,
      color,
      [
        _buildInfoRow('Expires At', expInfo['expires_at'] ?? 'Unknown'),
        _buildInfoRow('Is Expired', isExpired ? 'YES ❌' : 'NO ✅'),
        if (!isExpired)
          _buildInfoRow('Time Until Expiry', expInfo['time_until_expiry'] ?? 'Unknown'),
      ],
    );
  }

  Widget _buildValidationIssues(List<dynamic> issues) {
    return _buildSection(
      'Validation Issues',
      Icons.warning,
      Colors.orange,
      issues.map((issue) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
            Expanded(child: Text(issue.toString())),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildFullTokenSection() {
    final result = _analysisResult!;
    final tokenPreview = result['token_preview'] ?? '';
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.content_copy, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Full Token',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: _copyFullToken,
                icon: const Icon(Icons.copy, size: 16),
                label: const Text('Copy'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '$tokenPreview...',
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _analyzeToken() async {
    setState(() {
      _isAnalyzing = true;
      _error = null;
      _analysisResult = null;
    });

    try {
      final result = await AuthClient.staticDecodeJwtToken();
      
      if (result == null) {
        setState(() {
          _error = 'No token analysis result returned';
          _isAnalyzing = false;
        });
        return;
      }
      
      if (result.containsKey('error')) {
        setState(() {
          _error = result['error'].toString();
          _isAnalyzing = false;
        });
        return;
      }
      
      setState(() {
        _analysisResult = result;
        _isAnalyzing = false;
      });
      
    } catch (e) {
      setState(() {
        _error = 'Analysis failed: $e';
        _isAnalyzing = false;
      });
    }
  }

  Future<void> _copyFullToken() async {
    try {
      // Get the full token from AuthClient
      final authClient = AuthClient();
      await authClient.initialize();
      final token = authClient.accessToken;
      
      if (token != null) {
        await Clipboard.setData(ClipboardData(text: token));
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Full JWT token copied to clipboard! 📋'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No token available to copy'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy token: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
