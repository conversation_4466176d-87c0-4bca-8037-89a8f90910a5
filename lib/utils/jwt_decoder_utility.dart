import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../services/auth_client.dart';

/// Utility class for JWT token decoding and analysis
/// This helps debug JWT validation issues by providing detailed token information
class JwtDecoderUtility {
  /// Decode and analyze the current Supabase JWT token
  static Future<void> analyzeCurrentToken() async {
    try {
      debugPrint('🚀 Starting JWT Token Analysis...');
      debugPrint('=' * 60);
      
      final result = await AuthClient.staticDecodeJwtToken();
      
      if (result == null) {
        debugPrint('❌ No token analysis result returned');
        return;
      }
      
      if (result.containsKey('error')) {
        debugPrint('❌ Token analysis error: ${result['error']}');
        if (result.containsKey('details')) {
          debugPrint('   Details: ${result['details']}');
        }
        return;
      }
      
      debugPrint('📊 TOKEN ANALYSIS RESULTS:');
      debugPrint('=' * 60);
      
      // Basic token info
      debugPrint('📏 Token Length: ${result['token_length']}');
      debugPrint('🔢 Parts Count: ${result['parts_count']}');
      debugPrint('👀 Token Preview: ${result['token_preview']}...');
      debugPrint('');
      
      // Header analysis
      if (result.containsKey('header')) {
        final header = result['header'] as Map<String, dynamic>;
        debugPrint('🔍 JWT HEADER:');
        debugPrint('   Algorithm: ${header['alg']}');
        debugPrint('   Type: ${header['typ']}');
        debugPrint('   Key ID: ${header['kid']}');
        debugPrint('');
        
        // Algorithm-specific advice
        final alg = header['alg'] as String?;
        if (alg != null) {
          if (alg.startsWith('ES')) {
            debugPrint('🔐 ALGORITHM ANALYSIS:');
            debugPrint('   ✅ Using ECDSA ($alg) - requires public key validation');
            debugPrint('   🔑 Server needs to fetch public key from Supabase JWKS endpoint');
            debugPrint('   🌐 JWKS URL should be: https://your-project.supabase.co/auth/v1/jwks');
          } else if (alg.startsWith('RS')) {
            debugPrint('🔐 ALGORITHM ANALYSIS:');
            debugPrint('   ✅ Using RSA ($alg) - requires public key validation');
            debugPrint('   🔑 Server needs to fetch public key from Supabase JWKS endpoint');
          } else if (alg.startsWith('HS')) {
            debugPrint('🔐 ALGORITHM ANALYSIS:');
            debugPrint('   ⚠️ Using HMAC ($alg) - requires shared secret');
            debugPrint('   🔑 Server needs the JWT secret from Supabase settings');
          }
        }
        debugPrint('');
      }
      
      // Payload analysis
      if (result.containsKey('payload')) {
        final payload = result['payload'] as Map<String, dynamic>;
        debugPrint('📦 JWT PAYLOAD:');
        debugPrint('   Issuer (iss): ${payload['iss']}');
        debugPrint('   Subject (sub): ${payload['sub']}');
        debugPrint('   Audience (aud): ${payload['aud']}');
        debugPrint('   Email: ${payload['email']}');
        debugPrint('   Role: ${payload['role']}');
        debugPrint('   JWT ID (jti): ${payload['jti']}');
        debugPrint('');
        
        // Check for Supabase-specific claims
        if (payload.containsKey('app_metadata')) {
          debugPrint('📱 App Metadata: ${payload['app_metadata']}');
        }
        if (payload.containsKey('user_metadata')) {
          debugPrint('👤 User Metadata: ${payload['user_metadata']}');
        }
        if (payload.containsKey('session_id')) {
          debugPrint('🔗 Session ID: ${payload['session_id']}');
        }
        debugPrint('');
      }
      
      // Expiration analysis
      if (result.containsKey('expiration_info')) {
        final expInfo = result['expiration_info'] as Map<String, dynamic>;
        debugPrint('⏰ EXPIRATION ANALYSIS:');
        debugPrint('   Expires At: ${expInfo['expires_at']}');
        debugPrint('   Current Time: ${expInfo['current_time']}');
        debugPrint('   Is Expired: ${expInfo['is_expired']}');
        debugPrint('   Time Until Expiry: ${expInfo['time_until_expiry']}');
        debugPrint('');
        
        if (expInfo['is_expired'] == true) {
          debugPrint('❌ TOKEN IS EXPIRED!');
          debugPrint('   This is likely why server validation is failing');
          debugPrint('   Solution: Refresh the token or re-authenticate');
          debugPrint('');
        }
      }
      
      // Signature analysis
      if (result.containsKey('signature_info')) {
        final sigInfo = result['signature_info'] as Map<String, dynamic>;
        debugPrint('✍️ SIGNATURE INFO:');
        debugPrint('   Length: ${sigInfo['length']}');
        debugPrint('   Preview: ${sigInfo['preview']}...');
        debugPrint('');
      }
      
      // Token source
      if (result.containsKey('token_source')) {
        debugPrint('🏷️ Token Source: ${result['token_source']}');
        debugPrint('');
      }
      
      // Validation issues
      if (result.containsKey('validation_issues')) {
        final issues = result['validation_issues'] as List<dynamic>;
        debugPrint('⚠️ VALIDATION ISSUES FOUND:');
        for (final issue in issues) {
          debugPrint('   ❌ $issue');
        }
        debugPrint('');
      }
      
      // Server validation recommendations
      debugPrint('🔧 SERVER VALIDATION RECOMMENDATIONS:');
      debugPrint('=' * 60);
      
      final header = result['header'] as Map<String, dynamic>?;
      final payload = result['payload'] as Map<String, dynamic>?;
      
      if (header != null && payload != null) {
        final alg = header['alg'] as String?;
        final iss = payload['iss'] as String?;
        final kid = header['kid'] as String?;
        
        if (alg != null && alg.startsWith('ES')) {
          debugPrint('1. ✅ Server should use ECDSA verification for $alg');
          debugPrint('2. 🔑 Server needs to fetch public key using kid: $kid');
          if (iss != null) {
            final jwksUrl = iss.endsWith('/') ? '${iss}jwks' : '$iss/jwks';
            debugPrint('3. 🌐 JWKS endpoint should be: $jwksUrl');
          }
          debugPrint('4. 📝 Server should validate:');
          debugPrint('   - Token signature using public key');
          debugPrint('   - Token expiration (exp claim)');
          debugPrint('   - Token issuer (iss claim)');
          debugPrint('   - Token audience (aud claim) if required');
        }
        
        if (payload['aud'] != null) {
          debugPrint('5. 🎯 Server should verify audience: ${payload['aud']}');
        }
        
        if (payload['role'] != null) {
          debugPrint('6. 👤 User role in token: ${payload['role']}');
        }
      }
      
      debugPrint('');
      debugPrint('🎉 JWT Token Analysis Complete!');
      debugPrint('=' * 60);
      
    } catch (e) {
      debugPrint('❌ JWT Analysis failed: $e');
    }
  }
  
  /// Quick token validation check
  static Future<bool> isTokenValid() async {
    try {
      final result = await AuthClient.staticDecodeJwtToken();
      if (result == null || result.containsKey('error')) {
        return false;
      }
      
      final expInfo = result['expiration_info'] as Map<String, dynamic>?;
      if (expInfo != null) {
        return expInfo['is_expired'] != true;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Get token algorithm
  static Future<String?> getTokenAlgorithm() async {
    try {
      final result = await AuthClient.staticDecodeJwtToken();
      if (result == null || result.containsKey('error')) {
        return null;
      }
      
      final header = result['header'] as Map<String, dynamic>?;
      return header?['alg'] as String?;
    } catch (e) {
      return null;
    }
  }
  
  /// Get token issuer
  static Future<String?> getTokenIssuer() async {
    try {
      final result = await AuthClient.staticDecodeJwtToken();
      if (result == null || result.containsKey('error')) {
        return null;
      }
      
      final payload = result['payload'] as Map<String, dynamic>?;
      return payload?['iss'] as String?;
    } catch (e) {
      return null;
    }
  }
}
