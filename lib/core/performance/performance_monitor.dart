import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Performance monitoring service to track frame rendering and identify bottlenecks
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._();

  // Frame timing tracking
  final List<Duration> _frameTimes = [];
  final List<int> _skippedFrames = [];
  Timer? _reportTimer;
  bool _isMonitoring = false;

  // Performance metrics
  int _totalFrames = 0;
  int _totalSkippedFrames = 0;
  Duration _averageFrameTime = Duration.zero;
  Duration _maxFrameTime = Duration.zero;

  // Thresholds
  // ignore: unused_field
  static const Duration _frameTimeThreshold = Duration(
    milliseconds: 16,
  ); // 60 FPS
  static const Duration _jankThreshold = Duration(milliseconds: 32); // 30 FPS
  static const int _maxStoredFrames = 1000;

  /// Start monitoring frame performance
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _resetMetrics();

    // Start frame timing observer
    SchedulerBinding.instance.addTimingsCallback(_onFrameTimings);

    // Start periodic reporting
    _reportTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _reportPerformanceMetrics();
    });

    debugPrint('🔍 Performance monitoring started');
  }

  /// Stop monitoring frame performance
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;

    // Remove frame timing observer
    SchedulerBinding.instance.removeTimingsCallback(_onFrameTimings);

    // Stop periodic reporting
    _reportTimer?.cancel();
    _reportTimer = null;

    debugPrint('🔍 Performance monitoring stopped');
  }

  /// Handle frame timing data
  void _onFrameTimings(List<FrameTiming> timings) {
    if (!_isMonitoring) return;

    for (final timing in timings) {
      final frameTime = timing.totalSpan;
      _totalFrames++;

      // Store frame time (with limit)
      _frameTimes.add(frameTime);
      if (_frameTimes.length > _maxStoredFrames) {
        _frameTimes.removeAt(0);
      }

      // Update max frame time
      if (frameTime > _maxFrameTime) {
        _maxFrameTime = frameTime;
      }

      // Check for jank (frames taking longer than 16ms)
      if (frameTime > _jankThreshold) {
        _totalSkippedFrames++;
        _skippedFrames.add(_totalFrames);

        // Log severe jank
        if (frameTime > const Duration(milliseconds: 100)) {
          debugPrint(
            '🚨 Severe jank detected: ${frameTime.inMilliseconds}ms frame',
          );
        }
      }
    }

    // Update average frame time
    if (_frameTimes.isNotEmpty) {
      final totalTime = _frameTimes.fold<Duration>(
        Duration.zero,
        (sum, time) => sum + time,
      );
      _averageFrameTime = Duration(
        microseconds: totalTime.inMicroseconds ~/ _frameTimes.length,
      );
    }
  }

  /// Report performance metrics
  void _reportPerformanceMetrics() {
    if (_totalFrames == 0) return;

    final jankPercentage = (_totalSkippedFrames / _totalFrames * 100);
    final avgFps =
        _averageFrameTime.inMicroseconds > 0
            ? 1000000 / _averageFrameTime.inMicroseconds
            : 0.0;

    debugPrint('📊 Performance Report:');
    debugPrint('   Total frames: $_totalFrames');
    debugPrint(
      '   Janky frames: $_totalSkippedFrames (${jankPercentage.toStringAsFixed(1)}%)',
    );
    debugPrint('   Average FPS: ${avgFps.toStringAsFixed(1)}');
    debugPrint('   Average frame time: ${_averageFrameTime.inMilliseconds}ms');
    debugPrint('   Max frame time: ${_maxFrameTime.inMilliseconds}ms');

    // Log to developer tools for analysis
    developer.log(
      'Performance metrics',
      name: 'PerformanceMonitor',
      time: DateTime.now(),
      level: jankPercentage > 5 ? 900 : 800, // Warning level for high jank
      error: jankPercentage > 10 ? 'High jank percentage detected' : null,
    );
  }

  /// Reset all metrics
  void _resetMetrics() {
    _frameTimes.clear();
    _skippedFrames.clear();
    _totalFrames = 0;
    _totalSkippedFrames = 0;
    _averageFrameTime = Duration.zero;
    _maxFrameTime = Duration.zero;
  }

  /// Get current performance metrics
  Map<String, dynamic> getMetrics() {
    final jankPercentage =
        _totalFrames > 0 ? (_totalSkippedFrames / _totalFrames * 100) : 0.0;
    final avgFps =
        _averageFrameTime.inMicroseconds > 0
            ? 1000000 / _averageFrameTime.inMicroseconds
            : 0.0;

    return {
      'isMonitoring': _isMonitoring,
      'totalFrames': _totalFrames,
      'skippedFrames': _totalSkippedFrames,
      'jankPercentage': jankPercentage,
      'averageFps': avgFps,
      'averageFrameTimeMs': _averageFrameTime.inMilliseconds,
      'maxFrameTimeMs': _maxFrameTime.inMilliseconds,
      'recentFrameTimes':
          _frameTimes.take(10).map((t) => t.inMilliseconds).toList(),
    };
  }

  /// Check if performance is currently poor
  bool get isPerformancePoor {
    if (_totalFrames < 10) return false; // Not enough data

    final jankPercentage = (_totalSkippedFrames / _totalFrames * 100);
    return jankPercentage > 5.0; // More than 5% jank is considered poor
  }

  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _resetMetrics();
  }
}

/// Provider for performance monitor
final performanceMonitorProvider = Provider<PerformanceMonitor>((ref) {
  final monitor = PerformanceMonitor();

  // Auto-start monitoring in debug mode
  if (kDebugMode) {
    monitor.startMonitoring();
  }

  ref.onDispose(() => monitor.dispose());
  return monitor;
});

/// Provider for current performance metrics
final performanceMetricsProvider = Provider<Map<String, dynamic>>((ref) {
  final monitor = ref.watch(performanceMonitorProvider);

  // Refresh every 5 seconds
  Timer.periodic(const Duration(seconds: 5), (_) {
    ref.invalidateSelf();
  });

  return monitor.getMetrics();
});

/// Provider to check if performance is currently poor
final isPerformancePoorProvider = Provider<bool>((ref) {
  final monitor = ref.watch(performanceMonitorProvider);
  return monitor.isPerformancePoor;
});
