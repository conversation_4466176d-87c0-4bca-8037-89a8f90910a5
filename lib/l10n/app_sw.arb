{"@@locale": "sw", "pageTitle": "<PERSON><PERSON><PERSON> wa Nyumbani", "@pageTitle": {"description": "The title for the main application page"}, "welcomeMessage": "<PERSON><PERSON>, {userName}!", "@welcomeMessage": {"description": "A welcome message that includes the user's name.", "placeholders": {"userName": {"type": "String", "example": "Mwana"}}}, "login": "Ingia", "@login": {"description": "Text for the login button"}, "loginFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@loginFailed": {"description": "Message shown when login fails"}, "logout": "To<PERSON>", "@logout": {"description": "Text for the logout button"}, "logoutFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "@logoutFailed": {"description": "Message shown when logout fails"}, "profileTitle": "<PERSON><PERSON><PERSON>", "@profileTitle": {"description": "Title for the user profile screen"}, "settingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "@settingsTitle": {"description": "Title for the settings screen"}, "themeMode": "<PERSON><PERSON> ya <PERSON>", "@themeMode": {"description": "Title for the theme mode setting"}, "language": "<PERSON><PERSON><PERSON>", "@language": {"description": "Title for the language setting"}, "privacyPolicy": "<PERSON><PERSON> ya <PERSON>", "@privacyPolicy": {"description": "Title for the privacy policy setting"}, "checkForUpdates": "<PERSON><PERSON>", "@checkForUpdates": {"description": "Title for the check for updates setting"}, "notifications": "Arifa", "@notifications": {"description": "Title for the notifications setting"}, "termsOfService": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>", "@termsOfService": {"description": "Title for the terms of service setting"}, "about": "<PERSON><PERSON><PERSON>", "@about": {"description": "Title for the about setting"}, "errorNetwork": "Hitila<PERSON> ya mtandao. Tafadhali jaribu tena.", "@errorNetwork": {"description": "Generic network error message"}, "@@comment": "", "itemsCount": "{count, plural, =0{Hakuna kifungu} =1{Kifungu 1} other{{count} vifungu}}", "@itemsCount": {"description": "Message showing number of items", "placeholders": {"count": {"type": "num", "example": 3}}}}