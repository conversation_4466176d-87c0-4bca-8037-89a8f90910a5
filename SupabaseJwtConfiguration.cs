using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System.Text.Json;

namespace AuthService.Configuration;

/// <summary>
/// Auto-configures JWT Bearer authentication for Supabase
/// Handles JWKS discovery, API key authentication, and proper token validation
/// </summary>
public static class SupabaseJwtConfiguration
{
    /// <summary>
    /// Configures JWT Bearer authentication for Supabase with auto-discovery
    /// </summary>
    public static IServiceCollection AddSupabaseJwtAuthentication(
        this IServiceCollection services,
        IConfiguration configuration,
        ILogger logger)
    {
        var supabaseUrl = configuration["SUPABASE_URL"];
        var supabaseAnonKey = configuration["SUPABASE_ANON_KEY"];
        var supabaseProjectId = configuration["SUPABASE_PROJECT_ID"];

        if (string.IsNullOrEmpty(supabaseUrl))
            throw new InvalidOperationException("SUPABASE_URL must be configured");
        
        if (string.IsNullOrEmpty(supabaseAnonKey))
            throw new InvalidOperationException("SUPABASE_ANON_KEY must be configured");
        
        if (string.IsNullOrEmpty(supabaseProjectId))
            throw new InvalidOperationException("SUPABASE_PROJECT_ID must be configured");

        logger.LogInformation("Configuring Supabase JWT authentication for URL: {SupabaseUrl}", supabaseUrl);

        // Register the custom configuration retriever
        services.AddSingleton<IConfigurationRetriever<OpenIdConnectConfiguration>>(provider =>
        {
            var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
            
            return new SupabaseConfigurationRetriever(
                httpClientFactory,
                supabaseAnonKey,
                loggerFactory.CreateLogger<SupabaseConfigurationRetriever>()
            );
        });

        // Configure JWT Bearer authentication
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                var issuer = $"{supabaseUrl}/auth/v1";
                var jwksUri = $"{supabaseUrl}/auth/v1/jwks";

                logger.LogInformation("JWT Issuer: {Issuer}", issuer);
                logger.LogInformation("JWKS URI: {JwksUri}", jwksUri);

                // Configure token validation parameters
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = issuer,
                    ValidateAudience = true,
                    ValidAudiences = new[] { "authenticated", supabaseProjectId },
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromSeconds(30),
                    ValidateIssuerSigningKey = true,
                    RequireSignedTokens = true,
                    RequireExpirationTime = true
                };

                // Configure the configuration manager with custom retriever
                var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                    jwksUri,
                    services.BuildServiceProvider().GetRequiredService<IConfigurationRetriever<OpenIdConnectConfiguration>>()
                )
                {
                    AutomaticRefreshInterval = TimeSpan.FromHours(24),
                    RefreshInterval = TimeSpan.FromHours(1)
                };

                options.ConfigurationManager = configurationManager;

                // Enhanced event handlers
                options.Events = CreateJwtBearerEvents(logger, supabaseUrl, supabaseProjectId);
            });

        return services;
    }

    private static JwtBearerEvents CreateJwtBearerEvents(ILogger logger, string supabaseUrl, string supabaseProjectId)
    {
        return new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                LogAuthenticationFailure(logger, context, supabaseUrl, supabaseProjectId);
                return Task.CompletedTask;
            },
            OnMessageReceived = context =>
            {
                LogTokenReceived(logger, context);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                LogTokenValidated(logger, context);
                return Task.CompletedTask;
            },
            OnChallenge = context =>
            {
                logger.LogWarning("JWT Challenge triggered: {Error} - {ErrorDescription}", 
                    context.Error, context.ErrorDescription);
                return Task.CompletedTask;
            }
        };
    }

    private static void LogAuthenticationFailure(ILogger logger, AuthenticationFailedContext context, 
        string supabaseUrl, string supabaseProjectId)
    {
        var exception = context.Exception;
        
        switch (exception)
        {
            case SecurityTokenInvalidSignatureException:
                logger.LogError("JWT signature validation failed: {Error}", exception.Message);
                LogTokenDebugInfo(logger, context.Request);
                break;
                
            case SecurityTokenSignatureKeyNotFoundException:
                logger.LogError("JWT signing key not found: {Error}", exception.Message);
                logger.LogInformation("Verify JWKS endpoint is accessible: {JwksUri}/jwks", supabaseUrl);
                break;
                
            case SecurityTokenInvalidAudienceException:
                logger.LogError("JWT audience validation failed: {Error}", exception.Message);
                logger.LogInformation("Expected audiences: 'authenticated' or '{ProjectId}'", supabaseProjectId);
                break;
                
            case SecurityTokenInvalidIssuerException:
                logger.LogError("JWT issuer validation failed: {Error}", exception.Message);
                logger.LogInformation("Expected issuer: {ExpectedIssuer}", $"{supabaseUrl}/auth/v1");
                break;
                
            case SecurityTokenExpiredException:
                logger.LogWarning("JWT token has expired: {Error}", exception.Message);
                break;
                
            default:
                logger.LogError(exception, "JWT authentication failed: {Error}", exception.Message);
                break;
        }
    }

    private static void LogTokenReceived(ILogger logger, MessageReceivedContext context)
    {
        if (string.IsNullOrEmpty(context.Token)) return;

        try
        {
            var parts = context.Token.Split('.');
            if (parts.Length < 2) return;

            var headerJson = DecodeBase64Url(parts[0]);
            var header = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(headerJson);

            if (header != null)
            {
                var algorithm = header.TryGetValue("alg", out var alg) ? alg.GetString() : "unknown";
                var keyId = header.TryGetValue("kid", out var kid) ? kid.GetString() : "unknown";

                logger.LogDebug("JWT received - Algorithm: {Algorithm}, KeyId: {KeyId}", algorithm, keyId);
                
                context.HttpContext.Items["jwt_algorithm"] = algorithm;
                context.HttpContext.Items["jwt_key_id"] = keyId;
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error parsing JWT header for debugging");
        }
    }

    private static void LogTokenValidated(ILogger logger, TokenValidatedContext context)
    {
        var principal = context.Principal;
        if (principal == null) return;

        var userId = principal.FindFirst("sub")?.Value;
        var email = principal.FindFirst("email")?.Value;
        var audience = principal.FindFirst("aud")?.Value;
        var issuer = principal.FindFirst("iss")?.Value;
        var role = principal.FindFirst("role")?.Value;

        logger.LogInformation("JWT validated successfully - User: {UserId}, Email: {Email}, " +
            "Audience: {Audience}, Issuer: {Issuer}, Role: {Role}",
            userId, email, audience, issuer, role);
    }

    private static void LogTokenDebugInfo(ILogger logger, HttpRequest request)
    {
        try
        {
            var authHeader = request.Headers.Authorization.ToString();
            if (authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring(7);
                var parts = token.Split('.');
                
                if (parts.Length >= 2)
                {
                    var headerJson = DecodeBase64Url(parts[0]);
                    var payloadJson = DecodeBase64Url(parts[1]);
                    
                    logger.LogDebug("JWT Header: {Header}", headerJson);
                    logger.LogDebug("JWT Payload (first 200 chars): {Payload}", 
                        payloadJson.Length > 200 ? payloadJson.Substring(0, 200) + "..." : payloadJson);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error extracting token debug info");
        }
    }

    private static string DecodeBase64Url(string base64Url)
    {
        var base64 = base64Url.PadRight(base64Url.Length + (4 - base64Url.Length % 4) % 4, '=')
            .Replace('-', '+')
            .Replace('_', '/');
        
        return System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(base64));
    }
}

/// <summary>
/// Custom configuration retriever for Supabase JWKS that includes API key authentication
/// </summary>
public class SupabaseConfigurationRetriever : IConfigurationRetriever<OpenIdConnectConfiguration>
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly string _supabaseAnonKey;
    private readonly ILogger<SupabaseConfigurationRetriever> _logger;

    public SupabaseConfigurationRetriever(
        IHttpClientFactory httpClientFactory,
        string supabaseAnonKey,
        ILogger<SupabaseConfigurationRetriever> logger)
    {
        _httpClientFactory = httpClientFactory;
        _supabaseAnonKey = supabaseAnonKey;
        _logger = logger;
    }

    public async Task<OpenIdConnectConfiguration> GetConfigurationAsync(
        string address, 
        IDocumentRetriever retriever, 
        CancellationToken cancel)
    {
        _logger.LogDebug("Retrieving JWKS configuration from: {Address}", address);

        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            
            // Add Supabase API key header
            httpClient.DefaultRequestHeaders.Add("apikey", _supabaseAnonKey);
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            
            var response = await httpClient.GetAsync(address, cancel);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to retrieve JWKS: {StatusCode} - {ReasonPhrase}", 
                    response.StatusCode, response.ReasonPhrase);
                throw new InvalidOperationException($"Failed to retrieve JWKS: {response.StatusCode}");
            }

            var jwksJson = await response.Content.ReadAsStringAsync(cancel);
            _logger.LogDebug("Retrieved JWKS: {JwksLength} characters", jwksJson.Length);

            // Parse the JWKS and create OpenIdConnectConfiguration
            var jwks = new JsonWebKeySet(jwksJson);
            var configuration = new OpenIdConnectConfiguration
            {
                Issuer = address.Replace("/jwks", ""), // Remove /jwks to get issuer
                JwksUri = address
            };

            // Add signing keys
            foreach (var key in jwks.Keys)
            {
                configuration.SigningKeys.Add(key);
                _logger.LogDebug("Added signing key: {KeyId}, Algorithm: {Algorithm}", 
                    key.Kid, key.Alg);
            }

            _logger.LogInformation("Successfully configured JWKS with {KeyCount} keys", jwks.Keys.Count);
            return configuration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving JWKS configuration from {Address}", address);
            throw;
        }
    }
}
