#!/bin/bash

# Script to test JWT token with curl
# Usage: ./scripts/test_jwt_with_curl.sh "YOUR_JWT_TOKEN_HERE"

if [ $# -eq 0 ]; then
    echo "❌ Usage: $0 \"YOUR_JWT_TOKEN_HERE\""
    echo "📝 Get the token from the app debug console after tapping 'Analyze JWT Token'"
    exit 1
fi

JWT_TOKEN="$1"

echo "🔍 Testing JWT Token with Server Endpoints"
echo "=" * 60

echo "📏 Token Length: ${#JWT_TOKEN}"
echo "👀 Token Preview: ${JWT_TOKEN:0:100}..."
echo ""

# Test 1: Market Data Endpoint (where the 401 error occurs)
echo "🧪 Test 1: Market Data Endpoint"
echo "🌐 URL: https://abraapp.undeclab.com/marketdata/api/watchlist"
echo "---"
curl -X GET "https://abraapp.undeclab.com/marketdata/api/watchlist" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -v \
  -w "\n📊 Response Time: %{time_total}s\n📊 HTTP Code: %{http_code}\n" \
  2>&1

echo ""
echo "=" * 60

# Test 2: Auth Service Test JWT Endpoint
echo "🧪 Test 2: Auth Service Test JWT Endpoint"
echo "🌐 URL: https://abraapp.undeclab.com/api/auth/test-jwt"
echo "---"
curl -X GET "https://abraapp.undeclab.com/api/auth/test-jwt" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -v \
  -w "\n📊 Response Time: %{time_total}s\n📊 HTTP Code: %{http_code}\n" \
  2>&1

echo ""
echo "=" * 60

# Test 3: Decode JWT locally to verify structure
echo "🧪 Test 3: Local JWT Analysis"
echo "---"

# Extract header
HEADER=$(echo "$JWT_TOKEN" | cut -d'.' -f1)
# Add padding if needed
while [ $((${#HEADER} % 4)) -ne 0 ]; do
    HEADER="${HEADER}="
done

# Extract payload
PAYLOAD=$(echo "$JWT_TOKEN" | cut -d'.' -f2)
# Add padding if needed
while [ $((${#PAYLOAD} % 4)) -ne 0 ]; do
    PAYLOAD="${PAYLOAD}="
done

echo "🔍 JWT Header (decoded):"
echo "$HEADER" | base64 -d 2>/dev/null | jq . 2>/dev/null || echo "$HEADER" | base64 -d 2>/dev/null

echo ""
echo "🔍 JWT Payload (decoded):"
echo "$PAYLOAD" | base64 -d 2>/dev/null | jq . 2>/dev/null || echo "$PAYLOAD" | base64 -d 2>/dev/null

echo ""
echo "🔍 JWT Signature (base64url):"
echo "$JWT_TOKEN" | cut -d'.' -f3 | head -c 50
echo "..."

echo ""
echo "🎉 JWT Analysis Complete!"
echo "=" * 60

# Test 4: Check JWKS endpoint
echo "🧪 Test 4: JWKS Endpoint Check"
echo "🌐 URL: https://ckuzrppfabxuigmzstci.supabase.co/auth/v1/jwks"
echo "---"
curl -X GET "https://ckuzrppfabxuigmzstci.supabase.co/auth/v1/jwks" \
  -H "Accept: application/json" \
  -v \
  -w "\n📊 Response Time: %{time_total}s\n📊 HTTP Code: %{http_code}\n" \
  2>&1

echo ""
echo "🔧 RECOMMENDATIONS:"
echo "1. Check if server accepts issuer: https://ckuzrppfabxuigmzstci.supabase.co/auth/v1"
echo "2. Verify server fetches keys from JWKS endpoint above"
echo "3. Ensure server supports ES256 algorithm"
echo "4. Check server validates exp, aud, iss claims correctly"
