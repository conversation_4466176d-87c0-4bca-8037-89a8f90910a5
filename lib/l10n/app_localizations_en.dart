// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get pageTitle => 'Home Page';

  @override
  String welcomeMessage(String userName) {
    return 'Hello, $userName!';
  }

  @override
  String get login => 'Log In';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get logout => 'Log Out';

  @override
  String get logoutFailed => 'Logout failed';

  @override
  String get profileTitle => 'Profile';

  @override
  String get settingsTitle => 'Settings';

  @override
  String get themeMode => 'Theme Mode';

  @override
  String get language => 'Language';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get checkForUpdates => 'Check for Updates';

  @override
  String get notifications => 'Notifications';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get about => 'About';

  @override
  String get errorNetwork => 'Network error. Please try again.';

  @override
  String itemsCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count items',
      one: '1 item',
      zero: 'No items',
    );
    return '$_temp0';
  }
}
