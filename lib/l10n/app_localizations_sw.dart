// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swahili (`sw`).
class AppLocalizationsSw extends AppLocalizations {
  AppLocalizationsSw([String locale = 'sw']) : super(locale);

  @override
  String get pageTitle => 'Ukura<PERSON> wa Nyumbani';

  @override
  String welcomeMessage(String userName) {
    return 'Habari, $userName!';
  }

  @override
  String get login => 'Ingia';

  @override
  String get loginFailed => 'Imeshindikana kuingia';

  @override
  String get logout => 'Toka';

  @override
  String get logoutFailed => 'Imeshindikana kutoka';

  @override
  String get profileTitle => 'Wasifu';

  @override
  String get settingsTitle => 'Mipangilio';

  @override
  String get themeMode => 'Hali ya Mandhari';

  @override
  String get language => 'Lugha';

  @override
  String get privacyPolicy => 'Sera ya Faragha';

  @override
  String get checkForUpdates => 'Angalia Sasisho';

  @override
  String get notifications => 'Arifa';

  @override
  String get termsOfService => 'Masharti ya Huduma';

  @override
  String get about => 'Kuhusu';

  @override
  String get errorNetwork => 'Hitilafu ya mtandao. Tafadhali jaribu tena.';

  @override
  String itemsCount(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count vifungu',
      one: 'Kifungu 1',
      zero: 'Hakuna kifungu',
    );
    return '$_temp0';
  }

  @override
  String get watchlistTitle => 'Orodha ya Ufuatiliaji';

  @override
  String get portfolioTitle => 'Mkoba';

  @override
  String get chartTitle => 'Chati';

  @override
  String get hubTitle => 'Kituo';

  @override
  String get marketHubTitle => 'Kituo cha Soko';

  @override
  String get rateUs => 'Tukadirishe';

  @override
  String get analyzeJwtToken => 'Chunguza Tokeni ya JWT';

  @override
  String get askAlim => 'Muulize Alim';

  @override
  String get tradingIdeas => 'Mawazo ya Biashara';

  @override
  String get marketAnalysis => 'Uchambuzi wa Soko';

  @override
  String get investmentTips => 'Vidokezo vya Uwekezaji';

  @override
  String get riskManagement => 'Usimamizi wa Hatari';

  @override
  String get portfolioIdeas => 'Mawazo ya Mkoba';

  @override
  String get articleScreen => 'Skrini ya Makala';

  @override
  String get ideas => 'Mawazo';

  @override
  String get renameWatchlist => 'Badilisha Jina la Orodha';

  @override
  String get watchlistName => 'Jina la Orodha ya Ufuatiliaji';

  @override
  String get cancel => 'Ghairi';

  @override
  String get rename => 'Badilisha Jina';

  @override
  String get save => 'Hifadhi';

  @override
  String get delete => 'Futa';

  @override
  String get edit => 'Hariri';

  @override
  String get add => 'Ongeza';

  @override
  String get search => 'Tafuta';

  @override
  String get loading => 'Inapakia...';

  @override
  String get error => 'Hitilafu';

  @override
  String get retry => 'Jaribu Tena';

  @override
  String get noData => 'Hakuna data inayopatikana';

  @override
  String get connectionError =>
      'Hitilafu ya muunganisho. Tafadhali angalia muunganisho wako wa intaneti';

  @override
  String get sessionExpired => 'Kipindi kimemalizika. Tafadhali ingia tena.';

  @override
  String get jwtAnalysisMessage =>
      'Inachunguza tokeni ya JWT... Angalia console ya debug kwa maelezo';

  @override
  String jwtValidationFailed(String error) {
    return 'Uthibitisho wa JWT Umeshindikana: $error';
  }

  @override
  String get jwtValidationSuccess => 'Uthibitisho wa JWT Umefanikiwa! ✅';

  @override
  String jwtAnalysisFailed(String error) {
    return 'Uchambuzi wa JWT umeshindikana: $error';
  }
}
