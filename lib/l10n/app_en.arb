{"@@locale": "en", "pageTitle": "Home Page", "@pageTitle": {"description": "The title for the main application page"}, "welcomeMessage": "Hello, {userName}!", "@welcomeMessage": {"description": "A welcome message that includes the user's name.", "placeholders": {"userName": {"type": "String", "example": "Mwana"}}}, "login": "Log In", "@login": {"description": "Text for the login button"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "Message shown when login fails"}, "logout": "Log Out", "@logout": {"description": "Text for the logout button"}, "logoutFailed": "Logout failed", "@logoutFailed": {"description": "Message shown when logout fails"}, "profileTitle": "Profile", "@profileTitle": {"description": "Title for the user profile screen"}, "settingsTitle": "Settings", "@settingsTitle": {"description": "Title for the settings screen"}, "themeMode": "Theme Mode", "@themeMode": {"description": "Title for the theme mode setting"}, "language": "Language", "@language": {"description": "Title for the language setting"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Title for the privacy policy setting"}, "checkForUpdates": "Check for Updates", "@checkForUpdates": {"description": "Title for the check for updates setting"}, "notifications": "Notifications", "@notifications": {"description": "Title for the notifications setting"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Title for the terms of service setting"}, "about": "About", "@about": {"description": "Title for the about setting"}, "errorNetwork": "Network error. Please try again.", "@errorNetwork": {"description": "Generic network error message"}, "itemsCount": "{count, plural, =0{No items} =1{1 item} other{{count} items}}", "@itemsCount": {"description": "Message showing number of items", "placeholders": {"count": {"type": "num", "example": 3}}}}