import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_sw.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('sw'),
  ];

  /// The title for the main application page
  ///
  /// In en, this message translates to:
  /// **'Home Page'**
  String get pageTitle;

  /// A welcome message that includes the user's name.
  ///
  /// In en, this message translates to:
  /// **'Hello, {userName}!'**
  String welcomeMessage(String userName);

  /// Text for the login button
  ///
  /// In en, this message translates to:
  /// **'Log In'**
  String get login;

  /// Message shown when login fails
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginFailed;

  /// Text for the logout button
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logout;

  /// Message shown when logout fails
  ///
  /// In en, this message translates to:
  /// **'Logout failed'**
  String get logoutFailed;

  /// Title for the user profile screen
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileTitle;

  /// Title for the settings screen
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsTitle;

  /// Title for the theme mode setting
  ///
  /// In en, this message translates to:
  /// **'Theme Mode'**
  String get themeMode;

  /// Title for the language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Title for the privacy policy setting
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Title for the check for updates setting
  ///
  /// In en, this message translates to:
  /// **'Check for Updates'**
  String get checkForUpdates;

  /// Title for the notifications setting
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Title for the terms of service setting
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Title for the about setting
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Generic network error message
  ///
  /// In en, this message translates to:
  /// **'Network error. Please try again.'**
  String get errorNetwork;

  /// Message showing number of items
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =0{No items} =1{1 item} other{{count} items}}'**
  String itemsCount(num count);

  /// Title for the watchlist screen
  ///
  /// In en, this message translates to:
  /// **'Watchlist'**
  String get watchlistTitle;

  /// Title for the portfolio screen
  ///
  /// In en, this message translates to:
  /// **'Portfolio'**
  String get portfolioTitle;

  /// Title for the chart screen
  ///
  /// In en, this message translates to:
  /// **'Chart'**
  String get chartTitle;

  /// Title for the hub/market screen
  ///
  /// In en, this message translates to:
  /// **'Hub'**
  String get hubTitle;

  /// Title for the market hub screen
  ///
  /// In en, this message translates to:
  /// **'Market Hub'**
  String get marketHubTitle;

  /// Text for rate us button
  ///
  /// In en, this message translates to:
  /// **'Rate Us'**
  String get rateUs;

  /// Text for JWT token analysis button
  ///
  /// In en, this message translates to:
  /// **'Analyze JWT Token'**
  String get analyzeJwtToken;

  /// Text for AI assistant button
  ///
  /// In en, this message translates to:
  /// **'Ask Alim'**
  String get askAlim;

  /// Title for trading ideas section
  ///
  /// In en, this message translates to:
  /// **'Trading Ideas'**
  String get tradingIdeas;

  /// Title for market analysis section
  ///
  /// In en, this message translates to:
  /// **'Market Analysis'**
  String get marketAnalysis;

  /// Title for investment tips section
  ///
  /// In en, this message translates to:
  /// **'Investment Tips'**
  String get investmentTips;

  /// Title for risk management section
  ///
  /// In en, this message translates to:
  /// **'Risk Management'**
  String get riskManagement;

  /// Title for portfolio ideas section
  ///
  /// In en, this message translates to:
  /// **'Portfolio Ideas'**
  String get portfolioIdeas;

  /// Tooltip for article screen button
  ///
  /// In en, this message translates to:
  /// **'Article Screen'**
  String get articleScreen;

  /// Title for ideas section
  ///
  /// In en, this message translates to:
  /// **'Ideas'**
  String get ideas;

  /// Title for rename watchlist dialog
  ///
  /// In en, this message translates to:
  /// **'Rename Watchlist'**
  String get renameWatchlist;

  /// Label for watchlist name input field
  ///
  /// In en, this message translates to:
  /// **'Watchlist Name'**
  String get watchlistName;

  /// Text for cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Text for rename button
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get rename;

  /// Text for save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Text for delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Text for edit button
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Text for add button
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Text for search functionality
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Text shown when content is loading
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Generic error text
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Text for retry button
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Message when no data is available
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// Message shown when there's a connection error
  ///
  /// In en, this message translates to:
  /// **'Connection error. Please check your internet connection'**
  String get connectionError;

  /// Message shown when user session expires
  ///
  /// In en, this message translates to:
  /// **'Session expired. Please sign in again.'**
  String get sessionExpired;

  /// Message shown when analyzing JWT token
  ///
  /// In en, this message translates to:
  /// **'Analyzing JWT token... Check debug console for details'**
  String get jwtAnalysisMessage;

  /// Message shown when JWT validation fails
  ///
  /// In en, this message translates to:
  /// **'JWT Validation Failed: {error}'**
  String jwtValidationFailed(String error);

  /// Message shown when JWT validation succeeds
  ///
  /// In en, this message translates to:
  /// **'JWT Validation Successful! ✅'**
  String get jwtValidationSuccess;

  /// Message shown when JWT analysis fails
  ///
  /// In en, this message translates to:
  /// **'JWT Analysis failed: {error}'**
  String jwtAnalysisFailed(String error);
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'sw'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'sw':
      return AppLocalizationsSw();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
