# JWT Token Debugging Tools

This document explains how to use the JWT debugging tools to analyze Supabase JWT tokens and understand why server validation might be failing.

## 🔧 Available Tools

### 1. In-App JWT Analyzer (Recommended)
- **Location**: Settings → "Analyze JWT Token" button (appears when logged in)
- **Usage**: Tap the button to analyze your current JWT token
- **Output**: Check the debug console for detailed analysis

### 2. Standalone Script
- **Location**: `scripts/jwt_decoder.dart`
- **Usage**: 
  ```bash
  # With token as argument
  dart scripts/jwt_decoder.dart "your-jwt-token-here"
  
  # Interactive mode (will prompt for token)
  dart scripts/jwt_decoder.dart
  ```

### 3. Programmatic Access
```dart
import 'package:abra/utils/jwt_decoder_utility.dart';

// Analyze current token
await JwtDecoderUtility.analyzeCurrentToken();

// Quick validation check
bool isValid = await JwtDecoderUtility.isTokenValid();

// Get token algorithm
String? algorithm = await JwtDecoderUtility.getTokenAlgorithm();

// Get token issuer
String? issuer = await JwtDecoderUtility.getTokenIssuer();
```

## 📊 What the Analysis Shows

### Token Structure
- **Token Length**: Total character count
- **Parts Count**: Should be 3 for valid JWT (header.payload.signature)
- **Token Preview**: First 100 characters for identification

### Header Analysis
- **Algorithm (alg)**: Encryption algorithm used (ES256, RS256, HS256, etc.)
- **Type (typ)**: Should be "JWT"
- **Key ID (kid)**: Identifier for the public key used to sign the token

### Payload Analysis
- **Issuer (iss)**: Who issued the token (Supabase URL)
- **Subject (sub)**: User ID
- **Audience (aud)**: Intended recipient
- **Email**: User's email address
- **Role**: User's role (authenticated, etc.)
- **Expiration (exp)**: When the token expires
- **Issued At (iat)**: When the token was created

### Validation Checks
- **Expiration Status**: Whether the token is expired
- **Supabase Origin**: Confirms token is from Supabase
- **Required Claims**: Checks for essential JWT claims

## 🔍 Common Issues and Solutions

### 1. Token Expired
**Symptom**: `is_expired: true` in analysis
**Solution**: 
- Refresh the token using Supabase client
- Re-authenticate the user
- Check if auto-refresh is enabled

### 2. Algorithm Mismatch
**Symptom**: Server can't validate ES256/RS256 tokens
**Solution**:
- Server needs to fetch public keys from Supabase JWKS endpoint
- JWKS URL format: `https://your-project.supabase.co/auth/v1/jwks`
- Server should use the `kid` (Key ID) to select the correct public key

### 3. Missing Claims
**Symptom**: Missing `aud`, `iss`, or `role` claims
**Solution**:
- Check Supabase project configuration
- Verify JWT settings in Supabase dashboard
- Ensure proper authentication flow

### 4. Invalid Token Structure
**Symptom**: Parts count ≠ 3
**Solution**:
- Token may be corrupted during transmission
- Check for proper base64 encoding
- Verify token storage and retrieval

## 🔐 Server-Side Validation Requirements

For **ES256 tokens** (most common with Supabase):

1. **Fetch Public Key**: Use the JWKS endpoint to get the public key
   ```
   GET https://your-project.supabase.co/auth/v1/jwks
   ```

2. **Select Correct Key**: Use the `kid` from token header to select the right key

3. **Verify Signature**: Use ECDSA with the public key to verify the token signature

4. **Validate Claims**:
   - Check `exp` (expiration) against current time
   - Verify `iss` (issuer) matches your Supabase project
   - Validate `aud` (audience) if required
   - Check `role` for authorization

## 🚀 Quick Start

1. **Run the in-app analyzer**:
   - Open your app
   - Go to Settings
   - Tap "Analyze JWT Token"
   - Check the debug console output

2. **Look for these key indicators**:
   - ✅ Token structure is valid (3 parts)
   - ✅ Token is not expired
   - ✅ Algorithm is ES256 (most common)
   - ✅ Issuer contains "supabase"
   - ✅ Has required claims (sub, email, role)

3. **If validation fails**:
   - Check if token is expired
   - Verify server is using correct JWKS endpoint
   - Ensure server supports the token's algorithm
   - Confirm server validates required claims

## 📝 Example Output

```
🔍 Starting comprehensive JWT token analysis...
✅ Token found, length: 891
🔑 Token (first 100 chars): eyJhbGciOiJFUzI1NiIsImtpZCI6InYxLWxvY2FsLWVzMjU2Iiwid...

📊 TOKEN ANALYSIS RESULTS:
============================================================
📏 Token Length: 891
🔢 Parts Count: 3
👀 Token Preview: eyJhbGciOiJFUzI1NiIsImtpZCI6InYxLWxvY2FsLWVzMjU2Iiwid...

🔍 JWT HEADER:
   Algorithm: ES256
   Type: JWT
   Key ID: v1-local-es256

🔐 ALGORITHM ANALYSIS:
   ✅ Using ECDSA (ES256) - requires public key validation
   🔑 Server needs to fetch public key from Supabase JWKS endpoint

📦 JWT PAYLOAD:
   Issuer: https://your-project.supabase.co/auth/v1
   Subject: 12345678-1234-1234-1234-123456789012
   Audience: authenticated
   Email: <EMAIL>
   Role: authenticated

⏰ EXPIRATION ANALYSIS:
   Expires At: 2024-01-15T10:30:00.000Z
   Current Time: 2024-01-15T09:15:00.000Z
   Is Expired: false
   Time Until Expiry: 1:15:00.000000

🔧 SERVER VALIDATION RECOMMENDATIONS:
============================================================
1. ✅ Server should use ECDSA verification for ES256
2. 🔑 Server needs to fetch public key using kid: v1-local-es256
3. 🌐 JWKS endpoint should be: https://your-project.supabase.co/auth/v1/jwks
4. 📝 Server should validate:
   - Token signature using public key
   - Token expiration (exp claim)
   - Token issuer (iss claim)
   - Token audience (aud claim) if required
```

## 🆘 Need Help?

If you're still having issues after using these tools:

1. **Check the analysis output** for specific error messages
2. **Verify your server's JWT validation implementation**
3. **Ensure your Supabase project configuration is correct**
4. **Test with a fresh token** to rule out expiration issues
5. **Compare working vs non-working tokens** to identify differences
