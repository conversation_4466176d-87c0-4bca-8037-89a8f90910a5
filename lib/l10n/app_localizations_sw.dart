// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swahili (`sw`).
class AppLocalizationsSw extends AppLocalizations {
  AppLocalizationsSw([String locale = 'sw']) : super(locale);

  @override
  String get pageTitle => 'Ukura<PERSON> wa Nyumbani';

  @override
  String welcomeMessage(String userName) {
    return 'Habari, $userName!';
  }

  @override
  String get login => 'Ingia';

  @override
  String get loginFailed => 'Imeshindikana kuingia';

  @override
  String get logout => 'Toka';

  @override
  String get logoutFailed => 'Imeshindikana kutoka';

  @override
  String get profileTitle => 'Wasifu';

  @override
  String get settingsTitle => 'Mipangilio';

  @override
  String get themeMode => 'Hali ya Mandhari';

  @override
  String get language => 'Lugha';

  @override
  String get privacyPolicy => 'Sera ya Faragha';

  @override
  String get checkForUpdates => 'Angalia Sasisho';

  @override
  String get notifications => 'Arifa';

  @override
  String get termsOfService => 'Masharti ya Huduma';

  @override
  String get about => 'Kuhusu';

  @override
  String get errorNetwork => 'Hitilafu ya mtandao. Tafadhali jaribu tena.';

  @override
  String itemsCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count vifungu',
      one: 'Kifungu 1',
      zero: 'Hakuna kifungu',
    );
    return '$_temp0';
  }
}
