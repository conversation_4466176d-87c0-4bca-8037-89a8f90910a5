{"@@locale": "en", "pageTitle": "Home Page", "@pageTitle": {"description": "The title for the main application page"}, "welcomeMessage": "Hello, {userName}!", "@welcomeMessage": {"description": "A welcome message that includes the user's name.", "placeholders": {"userName": {"type": "String", "example": "Mwana"}}}, "login": "Log In", "@login": {"description": "Text for the login button"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "Message shown when login fails"}, "logout": "Log Out", "@logout": {"description": "Text for the logout button"}, "logoutFailed": "Logout failed", "@logoutFailed": {"description": "Message shown when logout fails"}, "profileTitle": "Profile", "@profileTitle": {"description": "Title for the user profile screen"}, "settingsTitle": "Settings", "@settingsTitle": {"description": "Title for the settings screen"}, "themeMode": "Theme Mode", "@themeMode": {"description": "Title for the theme mode setting"}, "language": "Language", "@language": {"description": "Title for the language setting"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Title for the privacy policy setting"}, "checkForUpdates": "Check for Updates", "@checkForUpdates": {"description": "Title for the check for updates setting"}, "notifications": "Notifications", "@notifications": {"description": "Title for the notifications setting"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Title for the terms of service setting"}, "about": "About", "@about": {"description": "Title for the about setting"}, "errorNetwork": "Network error. Please try again.", "@errorNetwork": {"description": "Generic network error message"}, "itemsCount": "{count, plural, =0{No items} =1{1 item} other{{count} items}}", "@itemsCount": {"description": "Message showing number of items", "placeholders": {"count": {"type": "num", "example": "3"}}}, "watchlistTitle": "Watchlist", "@watchlistTitle": {"description": "Title for the watchlist screen"}, "portfolioTitle": "Portfolio", "@portfolioTitle": {"description": "Title for the portfolio screen"}, "chartTitle": "Chart", "@chartTitle": {"description": "Title for the chart screen"}, "hubTitle": "<PERSON><PERSON>", "@hubTitle": {"description": "Title for the hub/market screen"}, "marketHubTitle": "Market Hub", "@marketHubTitle": {"description": "Title for the market hub screen"}, "rateUs": "Rate Us", "@rateUs": {"description": "Text for rate us button"}, "analyzeJwtToken": "Analyze JWT Token", "@analyzeJwtToken": {"description": "Text for JWT token analysis button"}, "askAlim": "<PERSON>", "@askAlim": {"description": "Text for AI assistant button"}, "tradingIdeas": "Trading Ideas", "@tradingIdeas": {"description": "Title for trading ideas section"}, "marketAnalysis": "Market Analysis", "@marketAnalysis": {"description": "Title for market analysis section"}, "investmentTips": "Investment Tips", "@investmentTips": {"description": "Title for investment tips section"}, "riskManagement": "Risk Management", "@riskManagement": {"description": "Title for risk management section"}, "portfolioIdeas": "Portfolio Ideas", "@portfolioIdeas": {"description": "Title for portfolio ideas section"}, "articleScreen": "Article Screen", "@articleScreen": {"description": "Tooltip for article screen button"}, "ideas": "Ideas", "@ideas": {"description": "Title for ideas section"}, "renameWatchlist": "Rename Watchlist", "@renameWatchlist": {"description": "Title for rename watchlist dialog"}, "watchlistName": "Watchlist Name", "@watchlistName": {"description": "Label for watchlist name input field"}, "cancel": "Cancel", "@cancel": {"description": "Text for cancel button"}, "rename": "<PERSON><PERSON>", "@rename": {"description": "Text for rename button"}, "save": "Save", "@save": {"description": "Text for save button"}, "delete": "Delete", "@delete": {"description": "Text for delete button"}, "edit": "Edit", "@edit": {"description": "Text for edit button"}, "add": "Add", "@add": {"description": "Text for add button"}, "search": "Search", "@search": {"description": "Text for search functionality"}, "loading": "Loading...", "@loading": {"description": "Text shown when content is loading"}, "error": "Error", "@error": {"description": "Generic error text"}, "retry": "Retry", "@retry": {"description": "Text for retry button"}, "noData": "No data available", "@noData": {"description": "Message when no data is available"}, "connectionError": "Connection error. Please check your internet connection", "@connectionError": {"description": "Message shown when there's a connection error"}, "sessionExpired": "Session expired. Please sign in again.", "@sessionExpired": {"description": "Message shown when user session expires"}, "jwtAnalysisMessage": "Analyzing JWT token... Check debug console for details", "@jwtAnalysisMessage": {"description": "Message shown when analyzing JWT token"}, "jwtValidationFailed": "JWT Validation Failed: {error}", "@jwtValidationFailed": {"description": "Message shown when JWT validation fails", "placeholders": {"error": {"type": "String", "example": "Token expired"}}}, "jwtValidationSuccess": "JWT Validation Successful! ✅", "@jwtValidationSuccess": {"description": "Message shown when JWT validation succeeds"}, "jwtAnalysisFailed": "JWT Analysis failed: {error}", "@jwtAnalysisFailed": {"description": "Message shown when JWT analysis fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}}